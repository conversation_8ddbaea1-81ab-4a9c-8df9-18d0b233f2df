version: '3.8'

services:
  nginx:
    image: nginx:latest
    container_name: nginx
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # 证书映射
      - ./cert:/etc/nginx/cert
      # 配置文件映射
      - ./conf/nginx.conf:/etc/nginx/nginx.conf
      # 页面目录
      - ./html:/usr/share/nginx/html
      # 日志目录
      - ./log:/var/log/nginx
    privileged: true
    network_mode: "host"
