version: '3'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.10.2
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - es_data:/usr/share/elasticsearch/data
      - es_plugins:/usr/share/elasticsearch/plugins

  kibana:
    image: docker.elastic.co/kibana/kibana:7.10.2
    container_name: kibana
    ports:
      - "5601:5601"
    environment:
      - SERVER_NAME:kibana.example.org
      - ELASTICSEARCH_HOSTS:http://elasticsearch:9200
#    volumes:
#      - ./kibana/config/kibana.yml:/usr/share/kibana/config/kibana.yml

volumes:
  es_data:
    driver: local
  es_plugins:
    driver: local