
spring:
  application:
    name: mcp
  main:
    web-application-type: none
  ai:
    dashscope:
      api-key: ${DASH_SCOPE_API_KEY}
    mcp:
      client:
        stdio:
#          servers-configuration: classpath:/mcp-servers-config.json
          servers-configuration: classpath:/mcp-servers-baidu-map-config.json
          # 使用百度地图的 MCP，需要申请 ak：https://lbsyun.baidu.com/apiconsole/key
  mandatory-file-encoding: UTF-8

# 调试日志
logging:
  level:
    io:
      modelcontextprotocol:
        client: DEBUG
        spec: DEBUG

server:
  port: 8085
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
