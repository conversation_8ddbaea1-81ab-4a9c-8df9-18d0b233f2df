最近MCP（模型上下文协议）挺火的，Spring AI 也提供了开箱即用的 MCP 服务器搭建机制。这里简单总结一下 Java 开发者需要了解的 MCP 常见问题，顺便推荐几篇相关的优质文章。

MCP 是什么？

MCP（模型上下文协议）是由 Anthropic 公司在 2024 年 11 月推出的一个开放标准协议。它的核心目的是为大型语言模型（LLM）与外部工具、数据源和行为（Actions）之间建立一个标准化的交互“桥梁”。

为什么需要 MCP？

1. 弥补 LLM 短板： LLM 在精确计算、实时信息获取（如天气）、与特定系统交互（如数据库、本地文件、API）、执行精细化或定制化操作等方面存在局限。
2. 简化集成： 提供统一接口，开发者无需为对接各种不同的工具和数据源编写大量定制化代码，降低了集成复杂性。
3. 扩展能力： 使 LLM 能够安全、便捷地利用外部能力来完成更复杂、更贴近现实世界的任务。

MCP 可以用来做什么？

1. 让 LLM 使用地图服务（如百度/高德地图）进行路线规划和时间计算。
2. 让 LLM 通过 Puppeteer 控制浏览器进行网页自动化操作。
3. 让 LLM 直接操作 GitHub/GitLab 代码仓库（如创建仓库）。
4. 让 LLM 查询和操作数据库（MySQL, ES, Redis 等）。
5. 让 LLM 使用外部搜索服务来获取最新信息。
6. 让 LLM 访问和处理本地文件，构建个人知识库。
7. ......

MCP 与 Function Calling、Agent 有什么区别？

1. MCP: 是一个协议标准，侧重于定义 LLM 与外部工具/数据之间交互的接口规范，非常灵活。
2. Function Calling: 是一种机制，通常由特定模型或平台提供，允许 LLM 请求执行预定义的函数（通常在宿主应用代码中），关系更紧密，实现方式可能不统一。
3. Agent (智能体): 是一个更复杂的系统概念，指能自主规划并执行多步骤任务以达成目标的系统。Agent 常常会利用 MCP 或 Function Calling 等机制作为其执行任务所需的工具。

MCP 架构与组件：

1. MCP 主机 (Host): 运行 AI 应用的程序，如 Claude Desktop、支持 MCP 的 IDE 插件（VS Code 中的 Cline）、Cursor 等。是用户交互的终端。
2. MCP 客户端 (Client): 通常集成在主机内，负责根据配置与 MCP 服务器建立和管理连接（1:1）。
3. MCP 服务器 (Server): 一个实现了 MCP 协议的轻量级程序，负责暴露特定的功能（工具或数据访问接口）。它可以连接本地数据源或调用远程服务。开发者可以使用官方 SDK（目前有 TypeScript, Python, Java）进行开发。
4. 数据源 (Data Sources): 包括本地文件、数据库、服务，以及可通过 API 访问的远程服务（如天气 API、GitHub API）。

一个主机应用程序可以管理和包含多个客户端实例，每个客户端实例负责与一个配置好的 MCP 服务器进行对话。例如，如果 Claude Desktop 配置了连接到 GitHub MCP 服务器和天气 MCP 服务器，那么在 Claude Desktop 这个主机内部，就存在（至少概念上）一个负责与 GitHub 服务器通信的客户端实例，以及另一个负责与天气服务器通信的客户端实例。

MCP 工作流程：

1. 用户通过主机发送请求；
2. 主机将请求交给 LLM；
3. LLM 判断是否需要外部工具/数据，若需要则告知客户端；
4. 客户端根据配置找到对应的 MCP 服务器并发送请求；
5. MCP 服务器访问数据源获取信息或执行操作；
6. 数据源返回结果给 MCP 服务器；MCP 服务器将结果返回给客户端；客户端将结果交给 LLM；
7. LLM 利用这个结果生成最终回复，由主机呈现给用户。通信可以通过本地进程（stdio）或网络（HTTP/SSE）进行。

MCP 实现方式：

1. MCP 服务器可以通过标准输入/输出（stdio）与本地客户端进行进程间通信（适用于轻量级工具）。
2. 也可以通过 HTTP/SSE (Server-Sent Events) 提供远程服务访问能力（适用于需要独立部署的重量级工具）。
3. Spring AI 提供了对 Java 开发 MCP 客户端和服务器的专门支持。

如何获取或使用 MCP 服务？

可以从社区维护的列表（如 awesome-mcp-servers）或专门的平台（如 mcp.so, cursor.directory/mcp, pulsemcp.com）查找和获取别人分享的 MCP 服务器。然后在支持 MCP 的客户端（如 Claude Desktop, VS Code 的 Cline 插件，Cursor）中进行配置即可使用。

如何开发自己的 MCP 服务器？

可以使用官方提供的 SDK 进行开发，目前支持 TypeScript、Python 和 Java。需要按照 MCP 协议规范实现服务接口，暴露你想让 LLM 使用的功能。Spring AI 框架为 Java 开发者提供了便捷的 MCP 服务器搭建机制（基于 stdio 或 SSE）。

文章推荐：

1. 从原理到示例：Java开发玩转MCP - 阿里云开发者：从原理到示例：Java开发玩转MCP
2. MCP 实践：基于 MCP 架构实现知识库答疑系统 - 阿里云开发者：MCP 实践：基于 MCP 架构实现知识库答疑系统
3. 从零开始教你打造一个MCP客户端：从零开始教你打造一个MCP客户端

#AI  #好文分享
收起