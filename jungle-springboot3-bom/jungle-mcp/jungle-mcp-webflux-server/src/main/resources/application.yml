#
# Copyright 2025-2026 the original author or authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# spring.main.web-application-type=none

# NOTE: You must disable the banner and the console logging 
# to allow the STDIO transport to work !!!
server:
  port: 8082

spring:
  main:
    banner-mode: off
  application:
    name: mcp-sse-server
  datasource:
    username: root
    password: Jskfb@2021
    url: ***************************************
    type: com.zaxxer.hikari.HikariDataSource
  ai:
    mcp:
      server:
        name: my-weather-server
        version: 0.0.1
# logging.pattern.console=


mybatis-plus:
  mapper-locations: classpath:mapper/*Mapper.xml
