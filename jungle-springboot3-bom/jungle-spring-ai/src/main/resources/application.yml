server:
  port: 8180

spring:
  ai:
    mcp:
      client:
        enabled: false
        sse:
          #配置 mcp 服务器地址
          connections:
            server1:
              url: http://localhost:8181
    #        type: async
#        toolcallback:
#          enabled: true
#    model:
#      chat: zhipuai #指定自动配置的ChatModel，默认自动配置所有 openai
    openai:
      base-url: https://api.chatanywhere.tech
      api-key: sk-pyWQ3wgQKHsZNc4DF8J0BhCo0ZF8a7sos7jYR7oEnUID0aDR
      chat:
        options:
          model: gpt-4o-mini # gpt-4o、gpt-4o-mini、gpt-4-turbo、gpt-3.5-turbo
          temperature: 0.8
#          response-format: '{ "type": "json_object" }' #启用 JSON 模式,保证模型生成的结果是有效的 JSON
    #          tools: #模型可以调用的工具列表，函数返回结果作为模型输入

    zhipuai:
#      base-url: #https://open.bigmodel.cn/api/paas
      api-key: c0845fe257d84025a241057104bac150.WCx7C04moYOhXeJT
      chat:
        options:
          model: glm-4-flash
          temperature: 0.8
#          tools:
          #设置默认 ToolContext
          tool-context:
            id: 15
            author: jungle

#    vectorstore:
#      milvus:
#        client:
#          host: "localhost"
#          port: 19530
#          username: "root"
#          password: "milvus"
#        databaseName: "default"
#        collectionName: "vector_store"
#        embeddingDimension: 1536
#        indexType: IVF_FLAT
#        metricType: COSINE
