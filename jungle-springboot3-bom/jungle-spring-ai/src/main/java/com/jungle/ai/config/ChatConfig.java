package com.jungle.ai.config;

import org.springframework.context.annotation.Configuration;

@Configuration
public class ChatConfig {

    /**
     * 配置 ChatClient
     * @param zhiPuAiChatModel
     * @param tools
     * @return
     */
//    @Bean
//    public ChatClient zhiPuAiChatClient(@Qualifier("zhiPuAiChatModel") ChatModel zhiPuAiChatModel, ToolCallbackProvider tools) {
//        return ChatClient.builder(zhiPuAiChatModel)
//                .defaultToolCallbacks(tools)
//                .build();
//    }


}
