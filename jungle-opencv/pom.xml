<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>jungle-springboot-demo</artifactId>
        <groupId>com.jungle</groupId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>jungle-opencv</artifactId>

    <description>
        opencv demo
    </description>

    <properties>

    </properties>

    <dependencies>
        <!--        <dependency>-->
        <!--            <groupId>org.bytedeco</groupId>-->
        <!--            <artifactId>javacv-platform</artifactId>-->
        <!--            <version>1.5.11</version>-->
        <!--        </dependency>-->
        <!--opencv 官网-->
        <!-- <dependency>
            <groupId>org.opencv</groupId>
            <artifactId>opencv</artifactId>
            <version>4.11.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/opencv/opencv-4110.jar</systemPath>
        </dependency> -->

        <dependency>
            <groupId>org.openpnp</groupId>
            <artifactId>opencv</artifactId>
            <version>4.8.1-0</version>
        </dependency>
    </dependencies>

</project>