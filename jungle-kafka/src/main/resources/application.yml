spring:
  application:
    name: jungle-demo

  kafka:
    # Broker 地址（集群用逗号分隔）
    bootstrap-servers: 47.109.52.29:9092
    producer:
      retries: 3                   # 重试次数
      batch-size: 1000             # 批量发送消息数
      buffer-memory: 33554432      # 缓冲区大小（32MB）
#      key-serializer: org.apache.kafka.common.serialization.StringSerializer
#      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
    consumer:
      group-id: "jungle-demo-service"  # 消费者组
      auto-offset-reset: earliest  # 从最早偏移量开始消费
      max-poll-records: 4000       # 单次拉取最大消息数
      enable-auto-commit: true      # 启用自动提交
      auto-commit-interval: 1000   # 自动提交间隔（ms）
#      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer

      #批消费并发量，小于或等于Topic的分区数
      batch:
        concurrency: 3
