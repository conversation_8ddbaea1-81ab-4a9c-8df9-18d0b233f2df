<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>jungle-springboot-demo</artifactId>
        <groupId>com.jungle</groupId>
        <version>${revision}</version>
    </parent>

    <artifactId>jungle-springboot-learn-bom</artifactId>

    <packaging>pom</packaging>

    <modules>
        <module>jungle-spring-webflux</module>
        <module>jungle-spring-api</module>
        <module>jungle-algorithm</module>
    </modules>

    <properties>
    </properties>

</project>