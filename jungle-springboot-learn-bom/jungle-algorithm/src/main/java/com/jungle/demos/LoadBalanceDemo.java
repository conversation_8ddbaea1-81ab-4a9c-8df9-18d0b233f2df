package com.jungle.demos;

/**
 * 加权轮询负载均衡策略
 */
public class LoadBalanceDemo {

    public static void main(String[] args) {
        WeightedRoundRobin wrr = new WeightedRoundRobin();
        wrr.addServer("A平台", 1);
        wrr.addServer("B平台", 2);
        wrr.addServer("C平台", 3);

        for (int i = 0; i < 12; i++) {
            System.out.println("Request " + (i+1) + " -> " + wrr.getNext());
        }
    }
}
