package com.jungle.demos;

import java.util.ArrayList;
import java.util.List;

public class WeightedRoundRobin {
    private List<Server> servers = new ArrayList<>();
    private int index = -1;
    private int remaining = 0;

    static class Server {
        String name;
        int weight;
        int current;

        Server(String name, int weight) {
            this.name = name;
            this.weight = weight;
            this.current = weight;
        }
    }

    public void addServer(String name, int weight) {
        servers.add(new Server(name, weight));
        remaining += weight;
    }

    public String getNext() {
        if (remaining == 0) resetWeights();

        while (true) {
            index = (index + 1) % servers.size();
            Server server = servers.get(index);
            if (server.current > 0) {
                server.current--;
                remaining--;
                return server.name;
            }
        }
    }

    private void resetWeights() {
        for (Server s : servers) {
            s.current = s.weight;
            remaining += s.weight;
        }
    }
}