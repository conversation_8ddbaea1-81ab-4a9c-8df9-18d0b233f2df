<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.6</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <packaging>pom</packaging>

    <groupId>com.jungle</groupId>
    <artifactId>jungle-springboot-demo</artifactId>
    <version>${revision}</version>

    <name>jungle-springboot-demo</name>

    <description>
        jungle-springboot-demo
    </description>

    <modules>
        <module>jungle-opencv</module>
        <module>jungle-kafka</module>
        <module>jungle-springboot3-bom</module>
        <module>jungle-springboot-learn-bom</module>
        <module>jungle-rabbitmq</module>
        <module>jungle-rocketmq</module>
        <module>jungle-elasticsearch</module>
        <module>jungle-gis</module>
        <module>syeran-chat</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <revision>1.0.1</revision>
        <spring-boot.version>2.7.6</spring-boot.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

    </properties>

    <dependencies>
        <!-- SpringBoot的依赖配置-->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-dependencies</artifactId>-->
<!--            <version>${spring-boot.version}</version>-->
<!--            <type>pom</type>-->
<!--            <scope>import</scope>-->
<!--        </dependency>-->


    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.30</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <url/>
    <licenses>
        <license/>
    </licenses>
    <developers>
        <developer/>
    </developers>
    <scm>
        <connection/>
        <developerConnection/>
        <tag/>
        <url/>
    </scm>
</project>
